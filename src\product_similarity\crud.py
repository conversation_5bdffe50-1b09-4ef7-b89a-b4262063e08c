from typing import Dict, Any, Optional, List
import asyncpg
import json
from .db import with_connection, with_transaction
from .utils import normalize_product_id, validate_similarity_score
from .logging import log_error, log_debug

# ==================== 产品信息 CRUD ====================

@with_connection
async def upsert_product_info(conn: asyncpg.Connection, nm_id: int, product_info: Dict[str, Any]) -> bool:
    """
    插入或更新产品信息
    
    Args:
        conn: 数据库连接
        nm_id: 产品ID
        product_info: 产品信息字典
        
    Returns:
        操作是否成功
    """
    try:
        nm_id = normalize_product_id(nm_id)
        # 将 Python 字典转换为 JSON 字符串，以便存储到 JSONB 字段
        product_info_json = json.dumps(product_info, ensure_ascii=False)

        await conn.execute(
            """
            INSERT INTO pj_similar.product_info (nm_id, product_info)
            VALUES ($1, $2::jsonb)
            ON CONFLICT (nm_id)
            DO UPDATE SET
                product_info = EXCLUDED.product_info,
                updated_at = NOW()
            """,
            nm_id, product_info_json
        )
        log_debug(f"产品信息已保存: {nm_id}")
        return True
    except Exception as e:
        log_error(f"保存产品信息失败: {nm_id}", error=e)
        return False

@with_connection
async def get_product_info(conn: asyncpg.Connection, nm_id: int) -> Optional[Dict[str, Any]]:
    """
    获取产品信息
    
    Args:
        conn: 数据库连接
        nm_id: 产品ID
        
    Returns:
        产品信息字典或None
    """
    try:
        nm_id = normalize_product_id(nm_id)
        row = await conn.fetchrow(
            "SELECT product_info FROM pj_similar.product_info WHERE nm_id=$1",
            nm_id
        )
        if row:
            log_debug(f"获取产品信息成功: {nm_id}")
            # JSONB 字段返回的可能是字符串，需要解析为字典
            product_info = row["product_info"]
            if isinstance(product_info, str):
                return json.loads(product_info)
            else:
                return product_info
        return None
    except Exception as e:
        log_error(f"获取产品信息失败: {nm_id}", error=e)
        return None

@with_connection
async def delete_product_info(conn: asyncpg.Connection, nm_id: int) -> bool:
    """
    删除产品信息
    
    Args:
        conn: 数据库连接
        nm_id: 产品ID
        
    Returns:
        操作是否成功
    """
    try:
        nm_id = normalize_product_id(nm_id)
        result = await conn.execute(
            "DELETE FROM pj_similar.product_info WHERE nm_id=$1",
            nm_id
        )
        success = result.split()[-1] != "0"  # 检查是否有行被删除
        if success:
            log_debug(f"产品信息已删除: {nm_id}")
        return success
    except Exception as e:
        log_error(f"删除产品信息失败: {nm_id}", error=e)
        return False

@with_connection
async def get_products_by_ids(conn: asyncpg.Connection, nm_ids: List[int]) -> Dict[int, Dict[str, Any]]:
    """
    批量获取产品信息
    
    Args:
        conn: 数据库连接
        nm_ids: 产品ID列表
        
    Returns:
        产品ID到产品信息的映射字典
    """
    try:
        nm_ids = [normalize_product_id(nm_id) for nm_id in nm_ids]
        rows = await conn.fetch(
            "SELECT nm_id, product_info FROM pj_similar.product_info WHERE nm_id = ANY($1)",
            nm_ids
        )
        result = {}
        for row in rows:
            # JSONB 字段返回的可能是字符串，需要解析为字典
            product_info = row["product_info"]
            if isinstance(product_info, str):
                result[row["nm_id"]] = json.loads(product_info)
            else:
                result[row["nm_id"]] = product_info
        log_debug(f"批量获取产品信息成功: {len(result)}/{len(nm_ids)}")
        return result
    except Exception as e:
        log_error("批量获取产品信息失败", error=e)
        return {}

# ==================== 相似度结果 CRUD ====================

@with_connection
async def get_similarity(conn: asyncpg.Connection, id1: int, id2: int) -> Optional[asyncpg.Record]:
    """
    获取两个产品的相似度结果
    
    Args:
        conn: 数据库连接
        id1: 产品ID1
        id2: 产品ID2
        
    Returns:
        相似度记录或None
    """
    try:
        id1, id2 = normalize_product_id(id1), normalize_product_id(id2)
        id_low, id_high = sorted((id1, id2))
        
        row = await conn.fetchrow(
            """
            SELECT * FROM pj_similar.similarity_result
            WHERE product_id1=$1 AND product_id2=$2
            """,
            id_low, id_high
        )
        if row:
            log_debug(f"获取相似度结果成功: {id1} vs {id2}")
        return row
    except Exception as e:
        log_error(f"获取相似度结果失败: {id1} vs {id2}", error=e)
        return None

@with_connection
async def insert_similarity(
    conn: asyncpg.Connection, 
    id1: int, 
    id2: int,
    score: int, 
    mode: str, 
    model_used: str,
    reason: Optional[str] = None
) -> bool:
    """
    插入相似度结果
    
    Args:
        conn: 数据库连接
        id1: 产品ID1
        id2: 产品ID2
        score: 相似度分数
        mode: 比较模式
        model_used: 使用的模型
        reason: 分析原因
        
    Returns:
        操作是否成功
    """
    try:
        id1, id2 = normalize_product_id(id1), normalize_product_id(id2)
        score = validate_similarity_score(score)
        id_low, id_high = sorted((id1, id2))
        
        await conn.execute(
            """
            INSERT INTO pj_similar.similarity_result
              (product_id1, product_id2, similarity_score, comparison_mode, model_used, reason)
            VALUES ($1, $2, $3, $4, $5, $6)
            ON CONFLICT (LEAST(product_id1, product_id2), GREATEST(product_id1, product_id2))
            DO UPDATE SET
                similarity_score = EXCLUDED.similarity_score,
                comparison_mode = EXCLUDED.comparison_mode,
                model_used = EXCLUDED.model_used,
                reason = EXCLUDED.reason,
                created_at = NOW()
            """,
            id_low, id_high, score, mode, model_used, reason
        )
        log_debug(f"相似度结果已保存: {id1} vs {id2} = {score}")
        return True
    except Exception as e:
        log_error(f"保存相似度结果失败: {id1} vs {id2}", error=e)
        return False

@with_connection
async def get_product_similarities(conn: asyncpg.Connection, product_id: int, limit: int = 10) -> List[asyncpg.Record]:
    """
    获取某个产品的所有相似度结果
    
    Args:
        conn: 数据库连接
        product_id: 产品ID
        limit: 返回结果数量限制
        
    Returns:
        相似度记录列表
    """
    try:
        product_id = normalize_product_id(product_id)
        rows = await conn.fetch(
            """
            SELECT * FROM pj_similar.similarity_result
            WHERE product_id1 = $1 OR product_id2 = $1
            ORDER BY similarity_score DESC, created_at DESC
            LIMIT $2
            """,
            product_id, limit
        )
        log_debug(f"获取产品相似度列表成功: {product_id}, 数量: {len(rows)}")
        return rows
    except Exception as e:
        log_error(f"获取产品相似度列表失败: {product_id}", error=e)
        return []

@with_connection
async def get_top_similarities(conn: asyncpg.Connection, limit: int = 20) -> List[asyncpg.Record]:
    """
    获取相似度最高的产品对
    
    Args:
        conn: 数据库连接
        limit: 返回结果数量限制
        
    Returns:
        相似度记录列表
    """
    try:
        rows = await conn.fetch(
            """
            SELECT * FROM pj_similar.similarity_result
            ORDER BY similarity_score DESC, created_at DESC
            LIMIT $1
            """,
            limit
        )
        log_debug(f"获取高相似度产品对成功，数量: {len(rows)}")
        return rows
    except Exception as e:
        log_error("获取高相似度产品对失败", error=e)
        return []

# ==================== 缓存 CRUD ====================

@with_connection
async def set_cache(conn: asyncpg.Connection, key: str, value: Dict[str, Any], ttl: Optional[int] = None) -> bool:
    """
    设置缓存
    
    Args:
        conn: 数据库连接
        key: 缓存键
        value: 缓存值
        ttl: 过期时间（秒），None表示不过期
        
    Returns:
        操作是否成功
    """
    try:
        expires_at = None
        if ttl:
            expires_at = f"NOW() + INTERVAL '{ttl} seconds'"
        
        if expires_at:
            await conn.execute(
                """
                INSERT INTO pj_similar.cache (cache_key, cache_value, expires_at)
                VALUES ($1, $2::jsonb, """ + expires_at + """)
                ON CONFLICT (cache_key)
                DO UPDATE SET
                    cache_value = EXCLUDED.cache_value,
                    expires_at = EXCLUDED.expires_at,
                    updated_at = NOW()
                """,
                key, value
            )
        else:
            await conn.execute(
                """
                INSERT INTO pj_similar.cache (cache_key, cache_value)
                VALUES ($1, $2::jsonb)
                ON CONFLICT (cache_key)
                DO UPDATE SET
                    cache_value = EXCLUDED.cache_value,
                    updated_at = NOW()
                """,
                key, value
            )
        return True
    except Exception as e:
        log_error(f"设置缓存失败: {key}", error=e)
        return False

@with_connection
async def get_cache(conn: asyncpg.Connection, key: str) -> Optional[Dict[str, Any]]:
    """
    获取缓存
    
    Args:
        conn: 数据库连接
        key: 缓存键
        
    Returns:
        缓存值或None
    """
    try:
        row = await conn.fetchrow(
            """
            SELECT cache_value FROM pj_similar.cache
            WHERE cache_key = $1 
            AND (expires_at IS NULL OR expires_at > NOW())
            """,
            key
        )
        if row:
            cache_value = row["cache_value"]
            # 如果缓存值是字符串（双重序列化的结果），尝试解析为JSON
            if isinstance(cache_value, str):
                try:
                    import json
                    return json.loads(cache_value)
                except json.JSONDecodeError:
                    # 如果解析失败，返回原始字符串
                    return cache_value
            return cache_value
        return None
    except Exception as e:
        log_error(f"获取缓存失败: {key}", error=e)
        return None

@with_connection
async def delete_cache(conn: asyncpg.Connection, key: str) -> bool:
    """
    删除缓存
    
    Args:
        conn: 数据库连接
        key: 缓存键
        
    Returns:
        操作是否成功
    """
    try:
        result = await conn.execute(
            "DELETE FROM pj_similar.cache WHERE cache_key = $1",
            key
        )
        return result.split()[-1] != "0"
    except Exception as e:
        log_error(f"删除缓存失败: {key}", error=e)
        return False

@with_connection
async def cleanup_expired_cache(conn: asyncpg.Connection) -> int:
    """
    清理过期缓存

    Args:
        conn: 数据库连接

    Returns:
        清理的记录数
    """
    try:
        result = await conn.fetchval("SELECT pj_similar.cleanup_expired_cache()")
        log_debug(f"清理过期缓存完成，清理数量: {result}")
        return result or 0
    except Exception as e:
        log_error("清理过期缓存失败", error=e)
        return 0

# ==================== Basket样本数据 CRUD ====================

@with_connection
async def add_basket_sample(
    conn: asyncpg.Connection,
    basket: str,
    short_id: int
) -> bool:
    """
    添加basket样本数据（只记录成功的样本）

    Args:
        conn: 数据库连接
        basket: basket编号
        short_id: 短ID

    Returns:
        操作是否成功
    """
    try:
        await conn.execute(
            """
            INSERT INTO pj_similar.basket_samples (basket, short_id)
            VALUES ($1, $2)
            ON CONFLICT (basket, short_id) DO NOTHING
            """,
            basket, short_id
        )

        # 添加样本后重新计算范围
        await conn.execute("SELECT pj_similar.recalculate_basket_ranges()")

        log_debug(f"Basket样本已保存: basket={basket}, short_id={short_id}")
        return True
    except Exception as e:
        log_error(f"保存Basket样本失败: basket={basket}, short_id={short_id}", error=e)
        return False

@with_connection
async def get_basket_by_short_id(conn: asyncpg.Connection, short_id: int) -> Optional[Dict[str, Any]]:
    """
    根据short_id获取basket信息

    Args:
        conn: 数据库连接
        short_id: 短ID

    Returns:
        包含basket信息的字典，或None
    """
    try:
        # 首先尝试从范围映射表查找
        row = await conn.fetchrow(
            """
            SELECT basket, min_short_id, max_short_id, sample_count
            FROM pj_similar.basket_mapping
            WHERE $1 BETWEEN min_short_id AND max_short_id
            ORDER BY sample_count DESC
            LIMIT 1
            """,
            short_id
        )

        if row:
            return {
                "basket": row["basket"],
                "source": "mapping",
                "in_range": True,
                "sample_count": row["sample_count"]
            }

        # 如果范围映射中没有找到，查找最近的样本
        row = await conn.fetchrow(
            """
            SELECT basket, ABS(short_id - $1) as distance
            FROM pj_similar.basket_samples
            ORDER BY distance ASC
            LIMIT 1
            """,
            short_id
        )

        if row:
            return {
                "basket": row["basket"],
                "source": "nearest_sample",
                "in_range": False,
                "distance": row["distance"]
            }

        return None

    except Exception as e:
        log_error(f"获取Basket信息失败: short_id={short_id}", error=e)
        return None

@with_connection
async def cleanup_basket_samples(conn: asyncpg.Connection) -> int:
    """
    清理过期的basket样本

    Args:
        conn: 数据库连接

    Returns:
        清理的记录数
    """
    try:
        result = await conn.fetchval("SELECT pj_similar.cleanup_old_samples()")

        log_debug(f"清理过期Basket样本完成，清理数量: {result}")
        return result or 0
    except Exception as e:
        log_error("清理Basket样本失败", error=e)
        return 0

@with_connection
async def get_basket_stats(conn: asyncpg.Connection) -> Dict[str, Any]:
    """
    获取basket统计信息

    Args:
        conn: 数据库连接

    Returns:
        统计信息字典
    """
    try:
        # 获取样本统计
        sample_stats = await conn.fetchrow(
            """
            SELECT
                COUNT(*) as total_samples,
                COUNT(DISTINCT basket) as unique_baskets,
                MIN(created_at) as oldest_sample,
                MAX(created_at) as newest_sample
            FROM pj_similar.basket_samples
            """
        )

        # 获取映射统计
        mapping_stats = await conn.fetchrow(
            """
            SELECT
                COUNT(*) as total_mappings,
                SUM(sample_count) as total_mapped_samples,
                AVG(sample_count) as avg_samples_per_basket
            FROM pj_similar.basket_mapping
            """
        )

        return {
            "sample_stats": dict(sample_stats) if sample_stats else {},
            "mapping_stats": dict(mapping_stats) if mapping_stats else {}
        }

    except Exception as e:
        log_error("获取Basket统计信息失败", error=e)
        return {"error": str(e)}
