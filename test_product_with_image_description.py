#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试产品信息获取功能（包含图片描述）
"""

import asyncio
import json
from src.product_similarity.services.product import get_product_detail
from src.product_similarity.db import init_pool, close_pool
from test_product_ids import nm_ids

async def test_product_with_image_description():
    """测试产品信息获取功能（包含图片描述）"""
    try:
        # 初始化数据库连接池
        await init_pool()
        print("数据库连接池初始化完成")
        
        # 使用真实的产品ID进行测试
        test_product_id = int(nm_ids[0])  # 使用第一个真实产品ID
        
        print(f"开始测试产品信息获取: {test_product_id}")
        print("=" * 60)
        
        # 获取产品信息（包含图片描述）
        product_info = await get_product_detail(test_product_id, force_refresh=True)
        
        if product_info:
            print("✅ 产品信息获取成功!")
            print(f"产品ID: {test_product_id}")
            print(f"产品名称: {product_info.get('name', 'N/A')}")
            print(f"产品价格: {product_info.get('priceU', 'N/A')}")
            
            # 检查图片URL
            img_urls = product_info.get('product_img_urls', [])
            print(f"图片数量: {len(img_urls)}")
            for i, url in enumerate(img_urls, 1):
                print(f"  图片{i}: {url}")
            
            # 检查图片描述
            image_descriptions = product_info.get('image_descriptions', [])
            print(f"图片描述数量: {len(image_descriptions)}")
            
            for i, img_desc in enumerate(image_descriptions, 1):
                url = img_desc.get('url', 'N/A')
                description = img_desc.get('description', 'N/A')
                print(f"  图片{i}描述:")
                print(f"    URL: {url}")
                print(f"    描述: {description[:100]}..." if description and len(description) > 100 else f"    描述: {description}")
            
            # 检查合并的描述文本
            combined_desc = product_info.get('images_description_text', '')
            print(f"合并描述文本长度: {len(combined_desc)}")
            if combined_desc:
                print(f"合并描述预览: {combined_desc[:200]}...")
            
            print("\n" + "=" * 60)
            print("完整产品信息结构:")
            print(json.dumps(product_info, ensure_ascii=False, indent=2)[:1000] + "...")
            
        else:
            print("❌ 产品信息获取失败!")
            
    except Exception as e:
        print(f"测试失败: {e}")
        import traceback
        traceback.print_exc()
    
    finally:
        # 关闭数据库连接池
        await close_pool()
        print("数据库连接池已关闭")

async def test_multiple_products():
    """测试多个产品的图片描述功能"""
    try:
        await init_pool()
        print("=" * 60)
        print("测试多个产品的图片描述功能")
        print("=" * 60)
        
        # 测试前3个产品
        test_products = nm_ids[:3]
        
        for i, product_id in enumerate(test_products, 1):
            print(f"\n🔍 测试产品 {i}/{len(test_products)}: {product_id}")
            
            try:
                product_info = await get_product_detail(int(product_id), force_refresh=True)
                
                if product_info:
                    name = product_info.get('name', 'N/A')
                    img_count = len(product_info.get('product_img_urls', []))
                    desc_count = len(product_info.get('image_descriptions', []))
                    combined_desc_len = len(product_info.get('images_description_text', ''))
                    
                    print(f"  ✅ 成功: {name[:50]}...")
                    print(f"  📷 图片数量: {img_count}")
                    print(f"  📝 描述数量: {desc_count}")
                    print(f"  📄 合并描述长度: {combined_desc_len}")
                    
                    # 显示第一个图片描述
                    image_descriptions = product_info.get('image_descriptions', [])
                    if image_descriptions and image_descriptions[0].get('description'):
                        first_desc = image_descriptions[0]['description']
                        print(f"  🖼️ 首图描述: {first_desc[:100]}...")
                else:
                    print(f"  ❌ 获取失败")
                    
            except Exception as e:
                print(f"  ❌ 错误: {str(e)}")
        
    except Exception as e:
        print(f"批量测试失败: {e}")
        import traceback
        traceback.print_exc()
    
    finally:
        await close_pool()

if __name__ == "__main__":
    print("选择测试模式:")
    print("1. 单个产品详细测试")
    print("2. 多个产品批量测试")
    
    choice = input("请输入选择 (1 或 2): ").strip()
    
    if choice == "1":
        asyncio.run(test_product_with_image_description())
    elif choice == "2":
        asyncio.run(test_multiple_products())
    else:
        print("无效选择，默认运行单个产品测试")
        asyncio.run(test_product_with_image_description())
