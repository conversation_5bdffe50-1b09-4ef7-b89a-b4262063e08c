"""
相似度服务模块
封装产品比对逻辑，集成AI比较器
"""
import time
import json
import asyncio
from functools import partial
from typing import List, Dict, Any, Optional
import httpx

from .product import get_product_detail
from ..crud import get_similarity, insert_similarity
from ..utils import change_md, normalize_product_id, validate_similarity_score, timing_decorator
from ..config import settings
from ..logging import log_success, log_error, log_warning, log_debug

# ========================== AI比较器相关类 ===========================

class EndpointConfig:
    """保存单个模型调用端点的配置信息"""

    def __init__(self, api_key: str = None, base_url: str = None, url: str = None,
                 model: str = None, is_multimodal: bool = False):
        # 兼容新旧配置格式
        # 新格式使用 base_url，旧格式使用 url
        if base_url:
            self.url = base_url.rstrip("/")
        elif url:
            self.url = url.rstrip("/")
        else:
            raise ValueError("必须提供 base_url 或 url 参数")

        self.api_key = api_key
        self.model = model
        self.is_multimodal = is_multimodal

    def headers(self) -> Dict[str, str]:
        return {
            "Content-Type": "application/json",
            "Authorization": f"Bearer {self.api_key}",
        }

class RoundRobinSelector:
    """简单轮询选择器，在多个端点间切换"""

    def __init__(self, endpoints: List[EndpointConfig]):
        if not endpoints:
            raise ValueError("endpoint 列表不能为空！")
        self._eps = endpoints
        self._idx = 0

    def next(self) -> EndpointConfig:
        ep = self._eps[self._idx]
        self._idx = (self._idx + 1) % len(self._eps)
        return ep

    @property
    def all(self) -> List[EndpointConfig]:
        return self._eps

class AIProductComparer:
    """产品相似度比较器，支持纯文本、多模态或二者并用"""

    MM_COOLDOWN = 120  # 多模态 429/500 后冷却秒数
    RETRY_STATUS = {429, 500, 502, 503, 504}

    def __init__(
        self,
        text_endpoints: List[EndpointConfig],
        mm_endpoints: Optional[List[EndpointConfig]] = None,
        *,
        timeout: int = 120,
        temperature: float = 0.1,
    ) -> None:
        if not text_endpoints:
            raise ValueError("至少需要一个纯文本端点！")
        self.text_sel = RoundRobinSelector(text_endpoints)
        self.mm_sel = RoundRobinSelector(mm_endpoints) if mm_endpoints else None
        self.timeout = timeout
        self.temperature = temperature
        self._mm_disabled_until = 0

    def compare(
        self,
        product1: Dict[str, Any],
        product2: Dict[str, Any],
        *,
        mode: str = "both",  # text|multimodal|both
        convert: bool = False,
    ) -> Dict[str, Any]:
        p1_str = change_md(product1) if convert else json.dumps(product1, ensure_ascii=False)
        p2_str = change_md(product2) if convert else json.dumps(product2, ensure_ascii=False)
        prompt = SYSTEM_PROMPT_TEMPLATE.format(target_product=p1_str, product=p2_str)

        if mode == "multimodal":
            return self._call_with_retry(prompt, use_mm=True)
        elif mode == "text":
            return self._call_with_retry(prompt, use_mm=False)
        elif mode == "both":
            if self._mm_allowed():
                try:
                    return self._call_with_retry(prompt, use_mm=True)
                except Exception as exc:
                    if self._should_block_mm(exc):
                        self._mm_disabled_until = time.time() + self.MM_COOLDOWN
                        log_warning("多模态不可用，降级文本")
                    else:
                        raise
            return self._call_with_retry(prompt, use_mm=False)
        else:
            raise ValueError("mode 只能是 text/multimodal/both")

    def compare_by_ids(
        self,
        product_id1: int,
        product_id2: int,
        *,
        mode: str = "both",
        convert: bool = False,
    ) -> Dict[str, Any]:
        """通过两个产品ID直接获得匹配度（同步版本，用于线程池）"""
        import requests
        
        # 这里需要同步获取产品信息，因为在线程池中运行
        # 实际使用时会通过异步包装
        raise NotImplementedError("此方法应该通过异步包装调用")

    def _mm_allowed(self) -> bool:
        return self.mm_sel is not None and time.time() >= self._mm_disabled_until

    @staticmethod
    def _should_block_mm(exc: Exception) -> bool:
        if isinstance(exc, httpx.TimeoutException):
            return True
        if isinstance(exc, httpx.HTTPStatusError):
            return exc.response.status_code in AIProductComparer.RETRY_STATUS
        if isinstance(exc, RuntimeError) and ("429" in str(exc) or "timed out" in str(exc)):
            return True
        return False

    def _call_with_retry(self, prompt: str, *, use_mm: bool) -> Dict[str, Any]:
        """尝试所有备用端点，直到成功或全部失败"""
        selector = self.mm_sel if use_mm else self.text_sel
        eps = selector.all
        last_err: Optional[Exception] = None

        for ep in eps:
            if use_mm and not ep.is_multimodal:
                continue
            try:
                result = self._call_once(prompt, ep)
                # 添加实际使用的模型信息
                result["model_used"] = ep.model
                return result
            except Exception as err:
                # 处理HTTP状态错误
                if isinstance(err, httpx.HTTPStatusError):
                    status = err.response.status_code
                    if status not in self.RETRY_STATUS:
                        raise
                # 处理解析错误和其他可重试的错误
                elif isinstance(err, (ValueError, RuntimeError, json.JSONDecodeError)):
                    # 这些错误通常是模型返回格式问题，可以尝试下一个模型
                    pass
                else:
                    # 其他类型的错误，根据情况决定是否重试
                    pass

                log_warning(f"端点 {ep.url} ({ep.model}) 失败: {err}. 尝试下一个备用端点…")
                last_err = err
                continue

        if last_err:
            raise last_err
        raise RuntimeError("所有端点都失败了")

    def _call_once(self, prompt: str, ep: EndpointConfig) -> Dict[str, Any]:
        """调用单个端点（同步版本）"""
        import requests

        payload = {
            "model": ep.model,
            "temperature": self.temperature,
            "messages": [{"role": "system", "content": prompt}],
        }

        try:
            resp = requests.post(
                f"{ep.url}/v1/chat/completions",
                headers=ep.headers(),
                json=payload,
                timeout=self.timeout,
            )
            resp.raise_for_status()
        except requests.exceptions.RequestException as err:
            # 创建一个简单的响应对象用于错误处理
            class MockResponse:
                def __init__(self, status_code):
                    self.status_code = status_code

            mock_resp = MockResponse(getattr(resp, 'status_code', 500) if 'resp' in locals() else 500)
            raise httpx.HTTPStatusError("Request failed", request=None, response=mock_resp) from err

        data = resp.json()
        try:
            content = data["choices"][0]["message"]["content"].strip()
            return json.loads(content)
        except Exception as e:
            # 使用特定的异常类型，以便在重试逻辑中正确处理
            raise ValueError(f"解析模型返回失败: {e}\n内容: {data}")

# 系统提示词模板
SYSTEM_PROMPT_TEMPLATE = """ 
 #任务
-将两个产品的介绍进行对比,按照我的评分规则得出综合相似评分,总分数范围1-100:
1,判断两个产品的1级类目是否相同,分数5-10分
2,判断两个产品的3级类目是否相似,分数15-25分
3,判断两个产品的标题相似性,分数15-25分
4,判断两个产品的价格相似性,分数10-15分
5,判断两个产的图片描述相似性,分数20-35分

##思考过程
###产品文案参数思考过程
-如果不在1级类目中,大概率产品相似性不相关,应该给予较低的相识评价
-2级类目是产品的细分类,考虑到一个产品可能有多个二级类目,所以你需要理解两个产品都适合彼此的2级类目
-标题中涉及到产品名字,参数,功能,风格,使用场景,产品名字是首要思考的对象,其他参数其次
-如果两个产品的产品名字一致,参数偏差10%视为相似,偏差太大,需要考虑价格偏差,价格偏差15%以内视为相似.反之先观察价格再观察参数也成立
-图片描述主要查看形状，功能,使用场景,风格等维度考虑.与文案描述结合综合判断

##不相似产品定义
-如果两个产品一级类目无相似关系,价格和材质不同，安装方式不同，功能描述不同，参数差异差50%以上的产品,视为不相关产品,判定为1

##扣减规则
-如果两个产品一级类目不同，判定为20分
-如果两个产品一级类目相似，三级类目相似,但是参数偏差20%以上或者安装方式不同视为不相似,材质差异大,价格偏差40%以上视为不相似,判定为20-40分
-如果两个产品一级类目相似,形状差异大,如圆形与长条型,圆盘型与圆柱型,圆形与不规则型,扣10-20分
-如果两个产品使用场景差异大,如大厅顶灯与桌面台灯,室外路灯与室内灯具,先查看3级类目是否类似,如果不类似,扣15-25分.否则不扣分

##产品信息:
###产品1:
    {target_product}

###产品2:
    {product}

##输出要求
只需要输出1-100的综合相似值,不需要输出任何的推理思考过程.1为完全不相似,100为完全相似

##Output format:
{{"similar_scores":相似值,"reson":中文分析说明}}

##示例
{{"similar_scores":40,"reson":中文分析说明}}
 """.strip()

# ========================== 相似度服务 ===========================

# 全局AI比较器实例
_comparer: Optional[AIProductComparer] = None

def _get_comparer() -> AIProductComparer:
    """获取AI比较器实例（单例）"""
    global _comparer
    if _comparer is None:
        # 从配置获取端点信息
        openai_endpoints = settings.get_openai_endpoints()
        mm_endpoints = settings.get_multimodal_endpoints()
        
        if not openai_endpoints:
            raise RuntimeError("未配置OpenAI端点")
        
        # 转换为EndpointConfig对象
        text_eps = []
        for ep in openai_endpoints:
            text_eps.append(EndpointConfig(
                api_key=ep.get("api_key", ""),
                base_url=ep.get("base_url"),  # 新格式
                url=ep.get("url"),            # 旧格式
                model=ep.get("model", ""),
                is_multimodal=ep.get("is_multimodal", False)
            ))
        
        mm_eps = None
        if mm_endpoints:
            mm_eps = []
            for ep in mm_endpoints:
                mm_eps.append(EndpointConfig(
                    api_key=ep.get("api_key", ""),
                    base_url=ep.get("base_url"),  # 新格式
                    url=ep.get("url"),            # 旧格式
                    model=ep.get("model", ""),
                    is_multimodal=ep.get("is_multimodal", True)
                ))
        
        _comparer = AIProductComparer(
            text_endpoints=text_eps,
            mm_endpoints=mm_eps,
            timeout=settings.AI_TIMEOUT,
            temperature=settings.AI_TEMPERATURE
        )
        
        log_success("AI比较器初始化成功")

    return _comparer

@timing_decorator
async def compare_products_by_ids(
    product_id1: int,
    product_id2: int,
    *,
    mode: str = "text",
    convert: bool = False
) -> Dict[str, Any]:
    """
    通过两个产品ID直接获得匹配度

    Args:
        product_id1: 第一个产品的ID
        product_id2: 第二个产品的ID
        mode: 比较模式 (text|multimodal|both)
        convert: 是否转换为markdown格式

    Returns:
        包含相似度评分和分析说明的字典

    Raises:
        ValueError: 当无法获取产品信息时
    """
    # 标准化产品ID
    product_id1 = normalize_product_id(product_id1)
    product_id2 = normalize_product_id(product_id2)

    # 1. 检查结果缓存（双向）
    record = await get_similarity(product_id1, product_id2)
    if record:
        log_debug(f"从缓存获取相似度结果: {product_id1} vs {product_id2}")
        return {
            "similar_scores": record["similarity_score"],
            "reson": record["reason"] or "缓存结果",
            "comparison_mode": record["comparison_mode"],
            "model_used": record["model_used"],
            "cached": True
        }

    # 2. 获取两个产品的详细信息
    try:
        product1_info, product2_info = await asyncio.gather(
            get_product_detail(product_id1),
            get_product_detail(product_id2)
        )
        log_success(f"===================================================================")
        log_success(f"成功获取产品1信息: {product1_info}")
        log_success(f"-------------------------------------------------------------------")
        log_success(f"成功获取产品2信息: {product2_info}")
    except Exception as e:
        log_error(f"获取产品信息失败: {product_id1}, {product_id2}", error=e)
        raise ValueError(f"无法获取产品信息: {e}")

    # 3. 真正调用比对（线程池 → 避免阻塞 event loop）
    loop = asyncio.get_running_loop()
    comparer = _get_comparer()

    try:
        result = await loop.run_in_executor(
            None,
            partial(
                _sync_compare_products,
                comparer,
                product1_info,
                product2_info,
                mode,
                convert
            )
        )

        # 4. 入库
        score = validate_similarity_score(result.get("similar_scores", 1))
        reason = result.get("reson", "")
        model_used = result.get("model_used", "unknown")

        success = await insert_similarity(
            product_id1, product_id2,
            score, mode, model_used, reason
        )

        if success:
            log_success(f"相似度结果已保存: {product_id1} vs {product_id2} = {score}")
        else:
            log_warning(f"相似度结果保存失败: {product_id1} vs {product_id2}")

        # 添加额外信息
        result["comparison_mode"] = mode
        # model_used 已经在 _call_with_retry 中设置了
        result["cached"] = False

        return result

    except Exception as e:
        log_error(f"产品比对失败: {product_id1} vs {product_id2}", error=e)
        raise

def _sync_compare_products(
    comparer: AIProductComparer,
    product1: Dict[str, Any],
    product2: Dict[str, Any],
    mode: str,
    convert: bool
) -> Dict[str, Any]:
    """
    同步版本的产品比对（用于线程池）
    """
    return comparer.compare(product1, product2, mode=mode, convert=convert)

async def get_product_similarities_list(product_id: int, limit: int = 10) -> List[Dict[str, Any]]:
    """
    获取某个产品的相似度列表

    Args:
        product_id: 产品ID
        limit: 返回结果数量限制

    Returns:
        相似度结果列表
    """
    try:
        product_id = normalize_product_id(product_id)

        # 从数据库获取相似度记录
        from ..crud import get_product_similarities
        records = await get_product_similarities(product_id, limit)

        results = []
        for record in records:
            # 确定另一个产品的ID
            other_id = record["product_id2"] if record["product_id1"] == product_id else record["product_id1"]

            results.append({
                "product_id": other_id,
                "similarity_score": record["similarity_score"],
                "comparison_mode": record["comparison_mode"],
                "model_used": record["model_used"],
                "reason": record["reason"],
                "created_at": record["created_at"].isoformat() if record["created_at"] else None
            })

        log_debug(f"获取产品相似度列表: {product_id}, 数量: {len(results)}")
        return results

    except Exception as e:
        log_error(f"获取产品相似度列表失败: {product_id}", error=e)
        return []

async def batch_compare_products(
    product_pairs: List[tuple[int, int]],
    mode: str = "text",
    convert: bool = False,
    max_concurrent: int = 5
) -> List[Dict[str, Any]]:
    """
    批量比较产品相似度

    Args:
        product_pairs: 产品ID对列表
        mode: 比较模式
        convert: 是否转换为markdown格式
        max_concurrent: 最大并发数

    Returns:
        比较结果列表
    """
    if not product_pairs:
        return []

    # 创建信号量限制并发数
    semaphore = asyncio.Semaphore(max_concurrent)

    async def compare_with_semaphore(id1: int, id2: int):
        async with semaphore:
            try:
                result = await compare_products_by_ids(id1, id2, mode=mode, convert=convert)
                return {
                    "product_id1": id1,
                    "product_id2": id2,
                    "success": True,
                    "result": result
                }
            except Exception as e:
                log_error(f"批量比较失败: {id1} vs {id2}", error=e)
                return {
                    "product_id1": id1,
                    "product_id2": id2,
                    "success": False,
                    "error": str(e)
                }

    # 创建任务
    tasks = [compare_with_semaphore(id1, id2) for id1, id2 in product_pairs]

    # 执行批量比较
    results = await asyncio.gather(*tasks, return_exceptions=True)

    # 统计结果
    success_count = sum(1 for r in results if isinstance(r, dict) and r.get("success"))
    log_success(f"批量比较完成: {success_count}/{len(product_pairs)}")

    return results

async def get_top_similar_products(limit: int = 20) -> List[Dict[str, Any]]:
    """
    获取相似度最高的产品对

    Args:
        limit: 返回结果数量限制

    Returns:
        相似度结果列表
    """
    try:
        from ..crud import get_top_similarities
        records = await get_top_similarities(limit)

        results = []
        for record in records:
            results.append({
                "product_id1": record["product_id1"],
                "product_id2": record["product_id2"],
                "similarity_score": record["similarity_score"],
                "comparison_mode": record["comparison_mode"],
                "model_used": record["model_used"],
                "reason": record["reason"],
                "created_at": record["created_at"].isoformat() if record["created_at"] else None
            })

        log_debug(f"获取高相似度产品对: {len(results)}")
        return results

    except Exception as e:
        log_error("获取高相似度产品对失败", error=e)
        return []

async def refresh_similarity_cache(product_id1: int, product_id2: int, mode: str = "text") -> bool:
    """
    刷新相似度缓存

    Args:
        product_id1: 产品ID1
        product_id2: 产品ID2
        mode: 比较模式

    Returns:
        刷新是否成功
    """
    try:
        # 先删除现有缓存（通过重新计算覆盖）
        await compare_products_by_ids(product_id1, product_id2, mode=mode)
        log_success(f"相似度缓存刷新成功: {product_id1} vs {product_id2}")
        return True
    except Exception as e:
        log_error(f"相似度缓存刷新失败: {product_id1} vs {product_id2}", error=e)
        return False
