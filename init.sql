-- 产品相似度微服务数据库初始化脚本

-- 创建数据库（如果不存在）
-- CREATE DATABASE product_similarity;

-- 连接到数据库
-- \c product_similarity;

-- 创建schema
CREATE SCHEMA IF NOT EXISTS pj_similar;

-- 设置搜索路径
SET search_path TO pj_similar, public;

-- 创建产品信息表
CREATE TABLE IF NOT EXISTS pj_similar.product_info (
    nm_id        BIGINT PRIMARY KEY,
    product_info JSONB             NOT NULL,
    created_at   TIMESTAMPTZ       DEFAULT NOW(),
    updated_at   TIMESTAMPTZ       DEFAULT NOW()
);

-- 创建相似度结果表
CREATE TABLE IF NOT EXISTS pj_similar.similarity_result (
    id                    BIGSERIAL PRIMARY KEY,
    product_id1           BIGINT NOT NULL,
    product_id2           BIGINT NOT NULL,
    similarity_score      INTEGER,
    comparison_mode       TEXT,
    model_used            TEXT,
    reason                TEXT,
    created_at            TIMESTAMPTZ DEFAULT NOW(),
    
    -- 确保两个产品ID的组合是唯一的（不考虑顺序）
    -- 注意：PostgreSQL不支持在UNIQUE约束中使用函数，所以我们用索引代替
    UNIQUE (product_id1, product_id2)
);

-- 创建缓存表
CREATE TABLE IF NOT EXISTS pj_similar.cache (
    cache_key     TEXT PRIMARY KEY,
    cache_value   JSONB NOT NULL,
    expires_at    TIMESTAMPTZ,
    created_at    TIMESTAMPTZ DEFAULT NOW(),
    updated_at    TIMESTAMPTZ DEFAULT NOW()
);

-- 创建关键词分析结果表
CREATE TABLE IF NOT EXISTS pj_similar.product_analyze_similar_result (
    id                    SERIAL PRIMARY KEY,
    keyword               TEXT NOT NULL,
    target_product_id     BIGINT NOT NULL,
    avg_similarity        INTEGER NOT NULL DEFAULT 0,
    similar_count         INTEGER NOT NULL DEFAULT 0,
    competitor_count      INTEGER NOT NULL DEFAULT 0,
    valid_scores          INTEGER NOT NULL DEFAULT 0,
    created_at            TIMESTAMP DEFAULT NOW(),

    -- 确保关键词和目标产品ID的组合是唯一的
    UNIQUE (keyword, target_product_id)
);

-- 创建索引
CREATE INDEX IF NOT EXISTS idx_product_info_created_at ON pj_similar.product_info(created_at);
CREATE INDEX IF NOT EXISTS idx_product_info_updated_at ON pj_similar.product_info(updated_at);
CREATE INDEX IF NOT EXISTS idx_product_info_jsonb ON pj_similar.product_info USING GIN(product_info);

CREATE INDEX IF NOT EXISTS idx_similarity_product_id1 ON pj_similar.similarity_result(product_id1);
CREATE INDEX IF NOT EXISTS idx_similarity_product_id2 ON pj_similar.similarity_result(product_id2);
CREATE INDEX IF NOT EXISTS idx_similarity_score ON pj_similar.similarity_result(similarity_score);
CREATE INDEX IF NOT EXISTS idx_similarity_created_at ON pj_similar.similarity_result(created_at);
CREATE INDEX IF NOT EXISTS idx_similarity_mode ON pj_similar.similarity_result(comparison_mode);

CREATE INDEX IF NOT EXISTS idx_cache_expires_at ON pj_similar.cache(expires_at);

-- 关键词分析表索引
CREATE INDEX IF NOT EXISTS idx_keyword_analysis_keyword ON pj_similar.product_analyze_similar_result(keyword);
CREATE INDEX IF NOT EXISTS idx_keyword_analysis_target_product_id ON pj_similar.product_analyze_similar_result(target_product_id);
CREATE INDEX IF NOT EXISTS idx_keyword_analysis_created_at ON pj_similar.product_analyze_similar_result(created_at);
CREATE INDEX IF NOT EXISTS idx_keyword_analysis_avg_similarity ON pj_similar.product_analyze_similar_result(avg_similarity);

-- 创建唯一索引确保产品对的唯一性（不考虑顺序）
-- 先删除可能存在的重复数据
DELETE FROM pj_similar.similarity_result a USING pj_similar.similarity_result b
WHERE a.id > b.id
AND LEAST(a.product_id1, a.product_id2) = LEAST(b.product_id1, b.product_id2)
AND GREATEST(a.product_id1, a.product_id2) = GREATEST(b.product_id1, b.product_id2);

-- 然后创建唯一索引
CREATE UNIQUE INDEX IF NOT EXISTS idx_similarity_unique_pair
ON pj_similar.similarity_result(LEAST(product_id1, product_id2), GREATEST(product_id1, product_id2));

-- 创建清理过期缓存的函数
CREATE OR REPLACE FUNCTION pj_similar.cleanup_expired_cache()
RETURNS INTEGER AS $$
DECLARE
    deleted_count INTEGER;
BEGIN
    DELETE FROM pj_similar.cache 
    WHERE expires_at IS NOT NULL AND expires_at < NOW();
    
    GET DIAGNOSTICS deleted_count = ROW_COUNT;
    RETURN deleted_count;
END;
$$ LANGUAGE plpgsql;

-- 创建统计视图
CREATE OR REPLACE VIEW pj_similar.product_stats AS
SELECT 
    COUNT(*) as total_products,
    COUNT(*) FILTER (WHERE created_at >= NOW() - INTERVAL '1 day') as products_today,
    COUNT(*) FILTER (WHERE created_at >= NOW() - INTERVAL '7 days') as products_week,
    COUNT(*) FILTER (WHERE created_at >= NOW() - INTERVAL '30 days') as products_month
FROM pj_similar.product_info;

CREATE OR REPLACE VIEW pj_similar.similarity_stats AS
SELECT 
    COUNT(*) as total_comparisons,
    COUNT(*) FILTER (WHERE created_at >= NOW() - INTERVAL '1 day') as comparisons_today,
    COUNT(*) FILTER (WHERE created_at >= NOW() - INTERVAL '7 days') as comparisons_week,
    COUNT(*) FILTER (WHERE created_at >= NOW() - INTERVAL '30 days') as comparisons_month,
    AVG(similarity_score) as avg_similarity_score,
    MIN(similarity_score) as min_similarity_score,
    MAX(similarity_score) as max_similarity_score,
    COUNT(DISTINCT comparison_mode) as unique_modes,
    COUNT(DISTINCT model_used) as unique_models
FROM pj_similar.similarity_result;

CREATE OR REPLACE VIEW pj_similar.popular_products AS
SELECT
    product_id,
    SUM(comparison_count) as comparison_count,
    AVG(avg_similarity_score) as avg_similarity_score,
    MAX(last_compared_at) as last_compared_at
FROM (
    SELECT
        product_id1 as product_id,
        COUNT(*) as comparison_count,
        AVG(similarity_score) as avg_similarity_score,
        MAX(created_at) as last_compared_at
    FROM pj_similar.similarity_result
    GROUP BY product_id1

    UNION ALL

    SELECT
        product_id2 as product_id,
        COUNT(*) as comparison_count,
        AVG(similarity_score) as avg_similarity_score,
        MAX(created_at) as last_compared_at
    FROM pj_similar.similarity_result
    GROUP BY product_id2
) combined
GROUP BY product_id
ORDER BY comparison_count DESC;

-- 插入一些示例数据（可选）
-- INSERT INTO pj_similar.product_info (nm_id, product_info) VALUES 
-- (123456, '{"name": "示例产品1", "price": 100}'),
-- (789012, '{"name": "示例产品2", "price": 200}');

-- 设置权限（如果需要）
-- GRANT ALL PRIVILEGES ON SCHEMA pj_similar TO postgres;
-- GRANT ALL PRIVILEGES ON ALL TABLES IN SCHEMA pj_similar TO postgres;
-- GRANT ALL PRIVILEGES ON ALL SEQUENCES IN SCHEMA pj_similar TO postgres;

-- 完成初始化
SELECT 'Database initialization completed successfully!' as status;
