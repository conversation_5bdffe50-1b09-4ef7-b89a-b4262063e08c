"""
产品服务模块
封装产品详情获取逻辑，支持缓存
"""
import httpx
from typing import Dict, Any
import asyncio
import json
from ..crud import get_product_info, upsert_product_info
from ..utils import normalize_product_id, retry_async
from ..logging import log_success, log_error, log_warning, log_debug

# ========================== 智能URL解析模块 ===========================

def parse_product_urls(product_id: int) -> Dict[str, Any]:
    """
    智能解析产品URL，返回产品信息URL和主图URL

    Args:
        product_id: 产品ID

    Returns:
        包含产品URL、主图URL、basket等信息的字典
    """
    short_id = product_id // 100000
    part = product_id // 1000

    # 计算basket编号
    basket = ''
    if short_id <= 3486:
        if 0 <= short_id <= 143:
            basket = '01'
        elif 144 <= short_id <= 287:
            basket = '02'
        elif 288 <= short_id <= 431:
            basket = '03'
        elif 432 <= short_id <= 719:
            basket = '04'
        elif 720 <= short_id <= 1007:
            basket = '05'
        elif 1008 <= short_id <= 1061:
            basket = '06'
        elif 1062 <= short_id <= 1115:
            basket = '07'
        elif 1116 <= short_id <= 1169:
            basket = '08'
        elif 1170 <= short_id <= 1313:
            basket = '09'
        elif 1314 <= short_id <= 1601:
            basket = '10'
        elif 1602 <= short_id <= 1655:
            basket = '11'
        elif 1656 <= short_id <= 1919:
            basket = '12'
        elif 1920 <= short_id <= 2045:
            basket = '13'
        elif 2046 <= short_id <= 2189:
            basket = '14'
        elif 2190 <= short_id <= 2405:
            basket = '15'
        elif 2406 <= short_id <= 2621:
            basket = '16'
        elif 2622 <= short_id <= 2837:
            basket = '17'
        elif 2838 <= short_id <= 3053:
            basket = '18'
        elif 3054 <= short_id <= 3270:
            basket = '19'
        elif 3271 <= short_id <= 3486:
            basket = '20'
    else:
        delta = short_id - 3487
        basket_num = 21 + (delta // 216)
        basket = str(basket_num).zfill(2)

    # 构建URLs
    product_url = f"https://basket-{basket}.wbbasket.ru/vol{short_id}/part{part}/{product_id}/info/ru/card.json"
    main_image_url = f"https://basket-{basket}.wbbasket.ru/vol{short_id}/part{part}/{product_id}/images/big/1.webp"

    return {
        "product_url": product_url,
        "main_image_url": main_image_url,
        "basket": basket,
        "short_id": short_id,
        "part": part
    }

# ========================== 图片描述获取模块 ===========================

@retry_async(max_retries=3, delay=1.0, backoff=2.0)
async def fetch_image_description(image_url: str, api_base_url: str = "http://localhost") -> str:
    """
    获取单张图片的描述信息

    Args:
        image_url: 图片URL
        api_base_url: API基础URL，默认为 http://localhost

    Returns:
        图片描述文本

    Raises:
        Exception: 当获取图片描述失败时
    """
    try:
        # 构建API请求URL
        api_url = f"{api_base_url}/api/say-img-description/describe?url={image_url}"

        # 发送异步请求
        async with httpx.AsyncClient(timeout=30) as client:
            response = await client.get(api_url)
            response.raise_for_status()

            # 解析响应
            result = response.json()
            description = result.get("description", "")

            if not description:
                raise ValueError(f"图片描述为空: {image_url}")

            log_debug(f"成功获取图片描述: {image_url[:50]}... -> {description[:50]}...")
            return description

    except httpx.RequestError as e:
        log_error(f"请求图片描述API失败: {image_url}", error=e)
        raise Exception(f"图片描述API请求失败: {str(e)}")
    except httpx.HTTPStatusError as e:
        log_error(f"图片描述API返回错误状态: {e.response.status_code}, URL: {image_url}")
        raise Exception(f"图片描述API错误状态: {e.response.status_code}")
    except json.JSONDecodeError as e:
        log_error(f"解析图片描述响应失败: {image_url}", error=e)
        raise Exception(f"图片描述响应解析失败: {str(e)}")
    except Exception as e:
        log_error(f"获取图片描述时发生未知错误: {image_url}", error=e)
        raise Exception(f"获取图片描述失败: {str(e)}")

# ========================== 产品信息获取与缓存模块 ===========================

@retry_async(max_retries=3, delay=1.0, backoff=2.0)
async def fetch_and_cache_product_info(product_url: str, product_id: int, force_refresh: bool = False) -> Dict[str, Any]:
    """
    从远程获取产品信息并缓存到数据库

    Args:
        product_url: 产品信息API URL
        product_id: 产品ID
        force_refresh: 是否强制刷新缓存

    Returns:
        清洗后的产品信息字典

    Raises:
        Exception: 当获取或处理产品信息失败时
    """
    # 如果不强制刷新，先检查缓存
    if not force_refresh:
        cached = await get_product_info(product_id)
        if cached:
            log_debug(f"从缓存获取产品信息: {product_id}")
            return cached

    try:
        headers = {
            "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/138.0.0.0 Safari/537.36",
            "Accept": "application/json, text/plain, */*",
            "Referer": f"https://www.wildberries.ru/catalog/{product_id}/detail.aspx",
            "Origin": "https://www.wildberries.ru",
            "Sec-Fetch-Site": "same-site",
            "Sec-Fetch-Mode": "cors",
            "Sec-Fetch-Dest": "empty",
            "Accept-Language": "zh-CN,zh;q=0.9"
        }

        async with httpx.AsyncClient(timeout=20) as client:
            response = await client.get(product_url, headers=headers)
            response.raise_for_status()
            product_info = response.json()

            if not product_info:
                raise ValueError(f"产品信息为空: {product_id}")

            # 清洗和格式化产品信息
            cleaned_info = _clean_product_info(product_info)

            # 保存到缓存
            success = await upsert_product_info(product_id, cleaned_info)
            if success:
                log_debug(f"产品信息已缓存: {product_id}")
            else:
                log_warning(f"产品信息缓存失败: {product_id}")

            log_success(f"成功获取产品信息: {product_id}")
            return cleaned_info

    except httpx.HTTPStatusError as e:
        log_error(f"HTTP请求失败: {product_id}, 状态码: {e.response.status_code}")
        raise Exception(f"产品信息获取失败，HTTP状态码: {e.response.status_code}")
    except httpx.TimeoutException as e:
        log_error(f"请求超时: {product_id}")
        raise Exception(f"产品信息获取超时: {str(e)}")
    except json.JSONDecodeError as e:
        log_error(f"解析产品信息响应失败: {product_id}", error=e)
        raise Exception(f"产品信息响应解析失败: {str(e)}")
    except Exception as e:
        log_error(f"获取产品信息失败: {product_id}", error=e)
        raise Exception(f"获取产品信息失败: {str(e)}")

def _clean_product_info(product_info: Dict[str, Any]) -> Dict[str, Any]:
    """
    清洗和格式化产品信息

    Args:
        product_info: 原始产品信息

    Returns:
        清洗后的产品信息
    """
    # 清洗无效数据
    keys_to_remove = ['colors', 'full_colors', 'selling', 'media', 'data', 'slug', 'certificate']
    for key in keys_to_remove:
        product_info.pop(key, None)

    # 处理 options 字段为扁平结构
    options = {}
    for option in product_info.get('options', []):
        if 'name' in option and 'value' in option:
            options[option['name']] = option['value']
    product_info['options'] = options

    # 处理 grouped_options 字段为扁平结构
    grouped_options_child = {}
    for group in product_info.get('grouped_options', []):
        for option in group.get('options', []):
            if 'name' in option and 'value' in option:
                grouped_options_child[option['name']] = option['value']
    product_info['grouped_options'] = grouped_options_child

    return product_info

# ========================== 数据拼接模块 ===========================

def merge_product_data(product_info: Dict[str, Any], image_description: str, url_info: Dict[str, Any]) -> Dict[str, Any]:
    """
    将产品信息和图片描述合并成最终返回格式

    Args:
        product_info: 产品基础信息
        image_description: 主图描述
        url_info: URL解析信息

    Returns:
        合并后的完整产品信息
    """
    # 添加主图URL
    product_info["product_img_urls"] = [url_info["main_image_url"]]

    # 添加图片描述文本
    product_info["images_description_text"] = image_description

    return product_info









async def get_product_detail(nm_id: int, *, force_refresh: bool = False) -> Dict[str, Any]:
    """
    获取产品详情（重构版本）

    Args:
        nm_id: 产品ID
        force_refresh: 是否强制刷新缓存

    Returns:
        产品信息字典

    Raises:
        ValueError: 当产品ID无效时
        Exception: 当获取产品信息或图片描述失败时
    """
    nm_id = normalize_product_id(nm_id)

    try:
        # 1. 智能URL解析
        url_info = parse_product_urls(nm_id)
        log_debug(f"URL解析完成: {nm_id}, basket={url_info['basket']}")

        # 2. 并发获取产品信息和图片描述
        product_task = fetch_and_cache_product_info(url_info["product_url"], nm_id, force_refresh)
        image_desc_task = fetch_image_description(url_info["main_image_url"])

        # 3. 等待两个任务完成，任何一个失败都会抛出异常
        product_info, image_description = await asyncio.gather(
            product_task,
            image_desc_task
        )

        # 4. 数据拼接
        final_result = merge_product_data(product_info, image_description, url_info)

        log_success(f"成功获取完整产品信息: {nm_id}")
        return final_result

    except Exception as e:
        log_error(f"获取产品详情失败: {nm_id}", error=e)
        raise

async def get_products_batch(nm_ids: list[int], *, force_refresh: bool = False) -> Dict[int, Dict[str, Any]]:
    """
    批量获取产品详情
    
    Args:
        nm_ids: 产品ID列表
        force_refresh: 是否强制刷新缓存
        
    Returns:
        产品ID到产品信息的映射字典
    """
    if not nm_ids:
        return {}
    
    # 标准化产品ID
    nm_ids = [normalize_product_id(nm_id) for nm_id in nm_ids]
    
    # 并发获取产品信息
    tasks = []
    for nm_id in nm_ids:
        task = asyncio.create_task(
            get_product_detail(nm_id, force_refresh=force_refresh)
        )
        tasks.append((nm_id, task))
    
    results = {}
    for nm_id, task in tasks:
        try:
            product_info = await task
            results[nm_id] = product_info
        except Exception as e:
            log_error(f"批量获取产品信息失败: {nm_id}", error=e)
            # 继续处理其他产品，不中断整个批量操作
    
    log_success(f"批量获取产品信息完成: {len(results)}/{len(nm_ids)}")
    return results

async def refresh_product_cache(nm_id: int) -> bool:
    """
    刷新产品缓存
    
    Args:
        nm_id: 产品ID
        
    Returns:
        刷新是否成功
    """
    try:
        await get_product_detail(nm_id, force_refresh=True)
        log_success(f"产品缓存刷新成功: {nm_id}")
        return True
    except Exception as e:
        log_error(f"产品缓存刷新失败: {nm_id}", error=e)
        return False

async def validate_product_exists(nm_id: int) -> bool:
    """
    验证产品是否存在
    
    Args:
        nm_id: 产品ID
        
    Returns:
        产品是否存在
    """
    try:
        await get_product_detail(nm_id)
        return True
    except Exception:
        return False
